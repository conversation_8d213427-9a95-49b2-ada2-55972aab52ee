/* Tabs */
#RecentVisitors .Tabs {
    display: flex;
    gap: 0px !important;
    margin: 6px 0 10px;
}

#RecentVisitors .Tab {
    padding: 4px 8px;
    border-radius: 4px;
    text-decoration: none;
    color: inherit;
    background: rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

#RecentVisitors .TabActive {
    background: rgba(255, 255, 255, 0.15);
    font-weight: bold;
}

/* Stats Panel tweaks */
#RecentVisitors #visitorStatsPanel {
    margin-top: 8px;
}

#RecentVisitors .TrendCard {
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
}

#RecentVisitors .SparkWrap {
    position: relative;
}

#RecentVisitors .SparkTooltip {
    position: absolute;
    pointer-events: none;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    font-size: 11px;
    padding: 3px 6px;
    border-radius: 3px;
    transform: translate(-50%, -100%);
    white-space: nowrap;
}

/* Panels fade/slide transitions */
#RecentVisitors .PanelTransition {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 180ms ease, transform 200ms ease;
}

#RecentVisitors .PanelTransition[style*="display: none"] {
    opacity: 0;
    transform: translateY(6px);
}

/* KPI cards */
#RecentVisitors .KPIRow {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 8px;
    margin: 8px 0;
}

#RecentVisitors .KPI {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    padding: 8px;
}

#RecentVisitors .KPIName {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 4px;
}

#RecentVisitors .KPIValue {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
}

#RecentVisitors .KPI svg {
    width: 100%;
    height: 24px;
    display: block;
}

/* Range chips */
#RecentVisitors .Filters {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 8px 0;
    flex-wrap: wrap;
    max-width: 100%;
}

#RecentVisitors .RangeChip {
    padding: 3px 8px;
    border-radius: 999px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: inherit;
    cursor: pointer;
    flex: 0 0 auto;
    box-sizing: border-box;
    white-space: nowrap;
}

#RecentVisitors .RangeActive {
    background: rgba(255, 255, 255, 0.15);
}

#RecentVisitors #StatsFilters>span {
    flex: 0 0 auto;
}

/* Leaderboard */
#RecentVisitors .Leaderboard {
    margin-top: 10px;
}

#RecentVisitors .TopList {
    display: grid;
    gap: 6px;
}

#RecentVisitors .TopItem {
    display: grid;
    grid-template-columns: 28px 1fr auto auto;
    gap: 8px;
    align-items: center;
    padding: 6px;
    background: rgba(0, 0, 0, 0.08);
    border-radius: 4px;
}

#RecentVisitors .TopItem img.Avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
}

#RecentVisitors .TopMeta {
    font-size: 12px;
    opacity: 0.8;
}

/* Removed heatmap styles */

/* Styles for the toggle button */
#RecentVisitors .ModeSwitcher,
#RecentVisitors .StatsButton {
    float: right;
    margin-right: 10px;
    font-size: 16px;
    text-decoration: none;
}

#RecentVisitors .ModeSwitcher .toggle-text {
    margin-left: 6px;
    font-size: 12px;
    opacity: 0.85;
    vertical-align: middle;
}

/* Stats panel styles */
#RecentVisitors .StatsPanel {
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(var(--PageModuleContentBackgroundColor), 0.8);
    border: 1px solid rgba(var(--BorderColor), 0.2);
    border-radius: 4px;
}

#RecentVisitors .StatsPanel h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: rgba(var(--TextColor), 0.9);
}

#RecentVisitors .StatRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px;
    border-radius: 3px;
    background-color: rgba(var(--PageBackgroundColor), 0.3);
}

#RecentVisitors .StatLabel {
    font-weight: bold;
}

#RecentVisitors .StatValue {
    padding: 2px 6px;
    background-color: rgba(var(--PageBackgroundColor), 0.6);
    border-radius: 3px;
    font-size: 13px;
}

#RecentVisitors .ComingSoonMessage {
    margin-top: 10px;
    padding: 8px;
    background-color: rgba(var(--PageBackgroundColor), 0.2);
    border-radius: 4px;
    text-align: center;
    font-style: italic;
    color: rgba(var(--TextColor), 0.7);
}

/* Online status indicator */
#RecentVisitors .OnlineDot {
    display: inline-block;
    width: 3px;
    height: 3px;
    background-color: #2ecc71;
    border-radius: 50%;
    margin-right: 5px;
    vertical-align: middle;
}

/* Styles for the display modes */
#RecentVisitors .Classic,
#RecentVisitors .VisitorsGrid {
    transition: opacity 0.3s ease;
}

/* Compact view table styles */
#RecentVisitors .Classic {
    width: 100%;
    display: table;
    table-layout: fixed;
}

#RecentVisitors .Classic columnheadings {
    display: table-header-group;
    font-weight: bold;
}

#RecentVisitors .Classic columnheading {
    display: table-cell;
    padding: 5px;
    text-align: left;
}

#RecentVisitors .Classic columnheading:first-child {
    width: 75%;
    /* Visitor column takes up more space */
}

#RecentVisitors .Classic columnheading:last-child {
    width: 25%;
    /* Time column takes less space now that it includes streaks */
}

#RecentVisitors .Classic record {
    display: table-row;
}

#RecentVisitors .Classic recordcell {
    display: table-cell;
    padding: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#RecentVisitors .AvatarWrap {
    margin-right: 10px;
}

#RecentVisitors .Avatar {
    width: 36px;
    height: 36px;
    object-fit: cover;
}

/* Styles for the settings panel */
#RecentVisitors .SettingsPanel {
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(var(--PageModuleContentBackgroundColor), 0.8);
    border: 1px solid rgba(var(--BorderColor), 0.2);
    border-radius: 4px;
}

#RecentVisitors .SettingsPanel h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
}

#RecentVisitors .SettingRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.SettingRow label {
    font-weight: bold;
}

#RecentVisitors .SettingRow select {
    padding: 4px;
    border-radius: 3px;
    border: 1px solid rgba(var(--BorderColor), 0.3);
    background-color: rgba(var(--PageBackgroundColor), 0.8);
    color: rgba(var(--TextColor), 1);
}

#RecentVisitors .SaveFeedback {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    padding: 5px;
    background-color: rgba(var(--PageBackgroundColor), 0.5);
    border-radius: 3px;
}

#RecentVisitors .feedbackText {
    font-size: 12px;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

#RecentVisitors .SettingActions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
}

#RecentVisitors .SettingActions button {
    padding: 4px 10px;
    border-radius: 3px;
    border: 1px solid rgba(var(--BorderColor), 0.3);
    background-color: rgba(var(--PageBackgroundColor), 0.8);
    color: rgba(var(--TextColor), 1);
    cursor: pointer;
}

#RecentVisitors .SettingActions button:hover {
    background-color: rgba(var(--PageBackgroundColor), 1);
}

/* Styles for the full display mode */
#RecentVisitors .VisitorsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

#RecentVisitors .VisitorCard {
    display: flex;
    flex-direction: column;
    padding: 12px;
    border-radius: 6px;
    background-color: rgba(var(--PageModuleContentBackgroundColor), 0.3);
    border: 1px solid rgba(var(--BorderColor), 0.1);
    transition: all 0.3s ease;
}

/* Alternating background colors for visitor cards */
#RecentVisitors .VisitorCard:nth-child(odd) {
    background-color: rgba(0, 0, 0, 0.1);
}

#RecentVisitors .VisitorCard:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.1);
}

#RecentVisitors .VisitorCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#RecentVisitors .MainCardContent {
    display: flex;
    width: 100%;
}

#RecentVisitors .HistoryColumn {
    margin-top: 10px;
    border-top: 1px solid rgba(var(--BorderColor), 0.1);
    padding-top: 10px;
    width: 100%;
}

#RecentVisitors .HistoryHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

#RecentVisitors .HistoryHeader h4 {
    margin: 0;
    font-size: 13px;
    color: rgba(var(--TextColor), 0.8);
}

#RecentVisitors .HistoryLoading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    font-size: 12px;
    color: rgba(var(--TextColor), 0.6);
}

#RecentVisitors .HistoryVisitItem {
    padding: 6px 8px;
    margin-bottom: 5px;
    border-radius: 3px;
    background-color: rgba(var(--PageBackgroundColor), 0.3);
    display: flex;
    align-items: center;
    font-size: 12px;
}

#RecentVisitors .HistoryVisitItem:last-child {
    margin-bottom: 0;
}

#RecentVisitors .OnlineIndicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 10px;
    height: 10px;
    background-color: #2ecc71;
    border-radius: 50%;
    border: 2px solid rgba(var(--PageModuleContentBackgroundColor), 1);
}

#RecentVisitors .MemberInfo {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    overflow: hidden;
}

#RecentVisitors .MemberName {
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#RecentVisitors .MemberName a {
    font-weight: bold;
    text-decoration: none;
}

#RecentVisitors .MemberTitle {
    display: block;
    font-size: 11px;
    color: rgba(var(--TextColor), 0.6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#RecentVisitors .VisitTime {
    font-size: 12px;
    margin-bottom: 4px;
}

/* Visit streak styles */
#RecentVisitors .VisitStreak {
    margin-top: 4px;
    display: flex;
    align-items: center;
}

#RecentVisitors .StreakIndicator {
    display: inline-flex;
    align-items: center;
    color: #ff9800;
    font-weight: bold;
    font-size: 12px;
    margin-right: 5px;
}

/* Visit history modal */
#RecentVisitors .HistoryInfo {
    display: none;
    /* Hide this div since we're now showing the info button only with streaks */
}

#RecentVisitors .InfoButton {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

#RecentVisitors .InfoIcon {
    width: 20px;
    height: 20px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

#RecentVisitors .InfoIcon:hover {
    opacity: 1;
}

/* Remove the visit history modal styles since we're using inline history */
#RecentVisitors .HistoryModal,
#RecentVisitors .HistoryModalContent,
#RecentVisitors .HistoryModalHeader,
#RecentVisitors .CloseButton {
    display: none;
}

/* Notification styles (for JavaScript notifications) */
.visitor-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    transition: all 0.3s ease;
    transform: translateX(100%);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.visitor-notification.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.visitor-notification.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.visitor-notification.info {
    background: linear-gradient(135deg, #2196f3, #1976d2);
}

.visitor-notification.show {
    transform: translateX(0);
}
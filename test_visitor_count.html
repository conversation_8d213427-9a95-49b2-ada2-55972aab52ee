<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Visitor Count Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #005a87;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        select {
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Visitor Count Issue Test</h1>
        
        <div class="test-section">
            <h3>Test Visitor Count Comparison</h3>
            <p>This test simulates the visitor count comparison logic to identify the issue with value 15.</p>
            
            <label for="testVisitorCount">Select Visitor Count:</label>
            <select id="testVisitorCount">
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="15">15</option>
                <option value="20">20</option>
            </select>
            
            <button class="test-btn" onclick="testComparison()">Test Comparison</button>
            <button class="test-btn" onclick="clearOutput()">Clear Output</button>
        </div>

        <div class="test-section">
            <h3>Simulate Different PHP Values</h3>
            <p>Test how different PHP values compare with JavaScript form values:</p>
            
            <label for="simulatedPhpValue">Simulated PHP Value:</label>
            <select id="simulatedPhpValue">
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="15">15</option>
                <option value="20">20</option>
            </select>
            
            <button class="test-btn" onclick="testPhpComparison()">Test PHP Comparison</button>
        </div>

        <div id="output" class="console-output">
            Test output will appear here...
        </div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function testComparison() {
            const visitorCount = document.getElementById('testVisitorCount').value;
            
            log('=== Testing Visitor Count Comparison ===');
            log(`Selected visitor count: "${visitorCount}" (type: ${typeof visitorCount})`);
            
            // Test against different "PHP" values
            const phpValues = ['5', '10', '15', '20'];
            
            phpValues.forEach(phpValue => {
                const isEqual = visitorCount === phpValue;
                const isNotEqual = visitorCount !== phpValue;
                log(`Compare "${visitorCount}" !== "${phpValue}": ${isNotEqual} (equal: ${isEqual})`);
            });
            
            // Test type coercion
            log('--- Type Coercion Tests ---');
            const numValue = parseInt(visitorCount);
            log(`parseInt("${visitorCount}"): ${numValue} (type: ${typeof numValue})`);
            log(`String(${numValue}): "${String(numValue)}" (type: ${typeof String(numValue)})`);
            
            // Test the specific case that might be failing
            if (visitorCount === '15') {
                log('🔍 Testing specific case for value 15:');
                log(`"15" === "15": ${visitorCount === '15'}`);
                log(`"15" !== "15": ${visitorCount !== '15'}`);
                
                // Test potential issues
                const trimmed = visitorCount.trim();
                log(`Trimmed value: "${trimmed}" (length: ${trimmed.length})`);
                log(`Original length: ${visitorCount.length}`);
            }
        }

        function testPhpComparison() {
            const jsValue = document.getElementById('testVisitorCount').value;
            const phpValue = document.getElementById('simulatedPhpValue').value;
            
            log('=== Testing JS vs Simulated PHP Comparison ===');
            log(`JS Value: "${jsValue}" (type: ${typeof jsValue})`);
            log(`PHP Value: "${phpValue}" (type: ${typeof phpValue})`);
            log(`Comparison "${jsValue}" !== "${phpValue}": ${jsValue !== phpValue}`);
            
            // Test what would trigger the AJAX call
            if (jsValue !== phpValue) {
                log('✅ This would trigger AJAX call to fetch new visitor data');
            } else {
                log('❌ This would NOT trigger AJAX call - values are equal');
            }
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = 'Output cleared...<br>';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('Test page loaded. Ready to test visitor count comparison logic.');
        });
    </script>
</body>
</html>

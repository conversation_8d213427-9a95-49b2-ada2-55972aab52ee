<!-- Link to external CSS file -->
<link rel="stylesheet" type="text/css" href="https://kurisumaki.se/gamebanana/visitors/visitors.css">

<?php
// Set the default timezone
date_default_timezone_set('UTC');

require_once __DIR__ . '/common.php';

// Allow requests from GameBanana for the main page (JavaScript API calls will be handled by individual endpoints)
// Main page doesn't need strict origin checking since it's embedded in GameBanana
if (!vb_is_origin_allowed(['https://gamebanana.com'])) {
    // If not from GameBanana, show a warning but don't block completely
    echo "<!-- Warning: This app should be accessed through GameBanana.com -->";
}

// Enforce rate limiting (15 requests per 5 minute)
vb_enforce_rate_limit(15, 300);

require_once 'db_config.php';
require_once 'track_visitor.php';
require_once 'gamebanana_api.php';

// Get the current profile ID and visitor ID from GameBanana's API
$profileId = isset($_GET['_idProfile']) ? (int)$_GET['_idProfile'] : 0;
$visitorId = isset($_GET['_idMember']) ? (int)$_GET['_idMember'] : 0;

// Authenticate app access if user is logged in (for main page, don't use JSON response)
if ($profileId > 0) {
    // First try to use cached token
    $cachedToken = vb_get_cached_token($profileId, 487);
    if ($cachedToken === null) {
        // If no cached token, authenticate with API
        if (!vb_authenticate_app($profileId, 487)) {
            // For main page, show error message instead of JSON response
            echo "<div class='Error'>App authentication failed. Please try again.</div>";
            exit;
        }
    }
}

// Check if current user is the owner or if we have valid IDs
$isOwner = ($visitorId === $profileId);
$validVisitor = ($visitorId > 0);
$validProfile = ($profileId > 0);

// Only proceed if we have a valid profile
if (!$validProfile) {
    echo "<div class='Error'>Invalid profile ID</div>";
    exit;
}

// Get settings from database or use defaults
$settings = getVisitorSettings($profileId);
$displayMode = $settings['display_mode'] ?? 'full';
$visitorCount = $settings['visitor_count'] ?? 10;

// Debug logging
error_log("Final settings for profile {$profileId} - Display: {$displayMode}, Count: {$visitorCount} (type: " . gettype($visitorCount) . ")");

// Record this visit only if user is logged in
if ($validVisitor) {
    recordVisitor($profileId, $visitorId);
}

// Get recent visitors based on settings
$visitors = getRecentVisitors($profileId, $visitorCount);

// For each visitor, calculate their streak
foreach ($visitors as &$visitor) {
    $visitor['streak'] = calculateVisitStreak($profileId, $visitor['visitor_id']);
}
unset($visitor); // Unset reference

// If no visitors and this is a valid profile, add an example visit from GameBanana admin (ID 1)
if (empty($visitors) && $validProfile) {
    // Insert sample visit
    $db->query("INSERT INTO profile_visitors (profile_id, visitor_id, visit_time) 
                VALUES ({$profileId}, 1, UTC_TIMESTAMP())
                ON DUPLICATE KEY UPDATE visit_time = UTC_TIMESTAMP()");
    
    // Refresh the visitors list
    $visitors = getRecentVisitors($profileId, $visitorCount);
}

// Use shared helpers for time formatting
function formatTime($timestamp) { return vb_format_time($timestamp); }
function getTimeClass($timestamp) { return vb_time_class($timestamp); }

// Get member details using GameBanana API
function getMemberInfo($memberId) {
    // Check if member ID is valid
    if ($memberId <= 0) {
        return [
            'name' => 'Guest',
            'id' => 0,
            'avatar' => 'https://gamebanana.com/img/default_avatar.png',
            'title' => '',
            'medal_count' => 0,
            'is_founder' => false,
            'is_uploader' => false,
            'is_online' => false,
            'location' => ''
        ];
    }

    // Use static cache to avoid repeated API calls for the same member
    static $memberCache = [];
    
    // Return cached data if available
    if (isset($memberCache[$memberId])) {
        return $memberCache[$memberId];
    }
    
    // Try to get real data from GameBanana API
    $memberDetails = getMemberDetails($memberId);
    
    if ($memberDetails) {
        // Cache the result
        $memberCache[$memberId] = $memberDetails;
        return $memberDetails;
    }
    
    // If the API call fails, log the error
    error_log("Failed to get member info for ID: $memberId - Using fallback data");
    
    // Fallback to mock data if API call fails
    $mockNames = [
        '123456' => 'GamerX',
        '234567' => 'ModderPro',
        '345678' => 'PixelArtist',
        '456789' => 'LevelDesigner',
        '567890' => 'SoundGuru',
        '678901' => 'CodeWizard',
        '789012' => 'AssetCreator',
        '890123' => 'TerrorBytes',
        '901234' => 'UIDesigner',
        '111222' => 'GameFan42'
    ];
    
    $fallbackData = [
        'name' => isset($mockNames[$memberId]) ? $mockNames[$memberId] : 'Member_' . $memberId,
        'id' => $memberId,
        'avatar' => 'https://gamebanana.com/img/default_avatar.png',
        'title' => '',
        'medal_count' => 0,
        'is_founder' => false,
        'is_uploader' => false,
        'is_online' => false,
        'location' => ''
    ];
    
    // Cache the fallback data too
    $memberCache[$memberId] = $fallbackData;
    return $fallbackData;
}

// Compute dynamic base URL for API calls in this directory
$baseUrl = vb_compute_base_url();

// Get settings for this profile
function getVisitorSettings($profileId) {
    global $db;
    
    try {
        $query = "SELECT settings FROM profile_visitor_settings WHERE profile_id = ?";
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        $stmt->bind_param('i', $profileId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $settings = json_decode($row['settings'], true);
            error_log("Retrieved settings for profile {$profileId}: " . json_encode($settings));
            return $settings;
        } else {
            // Default settings
            $defaultSettings = [
                'display_mode' => 'lite',
                'visitor_count' => 10,
                'notify_on_visit' => 'true'
            ];
            error_log("Using default settings for profile {$profileId}: " . json_encode($defaultSettings));
            return $defaultSettings;
        }
    } catch (Exception $e) {
        error_log("Error retrieving settings: " . $e->getMessage());
        return [
            'display_mode' => 'lite',
            'visitor_count' => 10,
            'notify_on_visit' => 'true'
        ];
    }
}

// Save settings for this profile
function saveVisitorSettings($profileId, $settings) {
    global $db;
    
    try {
        $settingsJson = json_encode($settings);
        $query = "INSERT INTO profile_visitor_settings (profile_id, settings) 
                 VALUES (?, ?)
                 ON DUPLICATE KEY UPDATE settings = ?";
        
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        $stmt->bind_param('iss', $profileId, $settingsJson, $settingsJson);
        $result = $stmt->execute();
        $stmt->close();
        
        return $result;
    } catch (Exception $e) {
        error_log("Error saving settings: " . $e->getMessage());
        return false;
    }
}
?>

<!-- Mimicking GameBanana's module structure -->
<module id="RecentVisitors" class="PageModule StrangeBerryModule">
    <h2>
        <a href="https://gamebanana.com/apps/487">Recent Visitors</a>
        <?php if ($isOwner): ?>
        <a href="javascript:void(0);" id="SettingsToggle" class="ModeSwitcher" title="Module Settings" onclick="toggleSettingsPanel(event)">
            <span class="toggle-icon"><img src="https://i.imgur.com/vZWBXs3.png" alt=""></span>
        </a>
        <?php endif; ?>
    </h2>
    <div class="Tabs">
        <a href="javascript:void(0);" class="Tab TabActive" data-tab="visitors" aria-selected="true">Visitors</a>
        <a href="javascript:void(0);" class="Tab" data-tab="stats" aria-selected="false">Statistics</a>
        <a href="javascript:void(0);" class="Tab" data-tab="leaderboard" aria-selected="false">Leaderboards</a>
    </div>

    <!-- Leaderboards tab content (initially hidden) -->
    <div id="visitorLeaderboardPanel" class="StatsPanel PanelTransition" style="display:none;">
        <h3>Leaderboards</h3>
        <div class="Filters" id="LbFilters">
            <span>Period:</span>
            <button class="RangeChip" data-days="0">Alltime</button>
            <button class="RangeChip" data-days="30">30d</button>
            <button class="RangeChip RangeActive" data-days="7">7d</button>
        </div>
        <div class="TopList" id="LbList"></div>
        <div class="SettingActions" style="justify-content: space-between;">
            <button id="LbPrev">Prev</button>
            <div class="TopMeta" id="LbPageInfo"></div>
            <button id="LbNext">Next</button>
        </div>
    </div>
    <!-- Stats tab content (initially hidden) -->
    <div id="visitorStatsPanel" class="StatsPanel PanelTransition" style="display:none;">
        <h3>Visitor Statistics</h3>
        <div id="VisitorStatsContent">
            <div class="Filters" id="StatsFilters">
                <span>Period:</span>
                <button class="RangeChip" data-days="1">Daily</button>
                <button class="RangeChip" data-days="7">Weekly</button>
                <button class="RangeChip" data-days="30">Monthly</button>
                <button class="RangeChip RangeActive" data-days="0">Alltime</button>
            </div>
            <div class="KPIRow">
                <div class="KPI"><div class="KPIName" id="KpiUniqueName">Unique</div><div class="KPIValue" id="KpiUnique">—</div></div>
                <div class="KPI"><div class="KPIName" id="KpiVisitsName">Visits</div><div class="KPIValue" id="KpiVisits">—</div></div>
                <div class="KPI"><div class="KPIName" id="KpiDaysName">Active days</div><div class="KPIValue" id="KpiDays">—</div></div>
                <div class="KPI"><div class="KPIName">Longest streak</div><div class="KPIValue" id="KpiStreak">—</div><div class="KPISub" id="KpiStreakUser">—</div></div>
            </div>
            <!-- Range controls removed for lifetime stats -->
            <div class="TrendCard">
                <div class="SparkWrap">
                    <svg id="ProfileSparkline" viewBox="0 0 100 24" preserveAspectRatio="none" aria-label="Visits trend" role="img" style="width:100%; height:24px;"></svg>
                    <div id="SparkTooltip" class="SparkTooltip" style="display:none;"></div>
                </div>
            </div>
            <div class="Leaderboard">
                <h4>Top Visitors</h4>
                <div id="TopFansList" class="TopList"></div>
            </div>
        </div>
    </div>
    
    <?php if ($isOwner): ?>
    <!-- Settings panel (overlay; independent of tabs) -->
    <div id="visitorSettingsPanel" class="SettingsPanel" style="display:none;">
        <h3>Module Settings</h3>
        <div class="SettingRow">
            <label for="displayMode">Display Style:</label>
            <select id="displayMode">
                <option value="lite" <?php echo $displayMode == 'lite' ? 'selected' : ''; ?>>Compact List</option>
                <option value="full" <?php echo $displayMode == 'full' ? 'selected' : ''; ?>>Detailed Grid</option>
            </select>
        </div>
        <div class="SettingRow">
            <label for="visitorCount">Number of Visitors:</label>
            <select id="visitorCount">
                <option value="5" <?php echo $visitorCount == 5 ? 'selected' : ''; ?>>5</option>
                <option value="10" <?php echo $visitorCount == 10 ? 'selected' : ''; ?>>10</option>
                <option value="15" <?php echo $visitorCount == 15 ? 'selected' : ''; ?>>15</option>
                <option value="20" <?php echo $visitorCount == 20 ? 'selected' : ''; ?>>20</option>
            </select>
        </div>
        <div class="SettingRow">
            <label for="notifyOnVisit">
                <input type="checkbox" id="notifyOnVisit" <?php echo ($settings['notify_on_visit'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                Show notifications when someone visits your profile
            </label>
        </div>
        <div id="saveFeedback" class="SaveFeedback" style="display:none;">
            <span class="feedbackText">Saving <img src="https://files.gamebanana.com/bitpit/load.gif"> </span>
        </div>
        <div class="SettingActions">
            <button onclick="saveSettings()">Save</button>
            <button onclick="toggleSettingsPanel(event)">Cancel</button>
        </div>
    </div>
    <?php endif; ?>
    
    <div id="VisitorsTabContent" class="Content PanelTransition">
        <?php if (!$validVisitor): ?>
            <div class="Empty">Please login to view and be tracked in the visitors module</div>
        <?php elseif (empty($visitors)): ?>
            <div class="Empty">No recent visitors</div>
        <?php else: ?>
            <!-- Lite display mode -->
            <records class="Classic" style="<?php echo $displayMode == 'full' ? 'display:none;' : ''; ?>">
                <columnheadings>
                    <columnheading>Visitor</columnheading>
                    <columnheading>Time</columnheading>
                </columnheadings>
                <?php foreach ($visitors as $visitor): ?>
                    <?php 
                    // Skip visitors with invalid IDs
                    if ($visitor['visitor_id'] <= 0) continue;
                    $member = getMemberInfo($visitor['visitor_id']); 
                    ?>
                    <record>
                        <recordcell>
                            <a href="/members/<?php echo $visitor['visitor_id']; ?>" class="Iconic">
                                <?php if ($member['is_online']): ?>
                                <span class="OnlineDot" title="<?php echo !empty($member['location']) ? 'Online at ' . $member['location'] : 'Online'; ?>"></span>
                                <?php endif; ?>
                                <?php echo $member['name']; ?>
                            </a>
                        </recordcell>
                        <recordcell>
                            <time class="<?php echo getTimeClass($visitor['visit_time']); ?>" datetime="<?php echo date('c', strtotime($visitor['visit_time'])); ?>">
                                <?php echo formatTime($visitor['visit_time']); ?>
                            </time>
                        </recordcell>
                    </record>
                <?php endforeach; ?>
            </records>
            
            <!-- Full display mode -->
            <div class="VisitorsGrid" style="<?php echo $displayMode == 'lite' ? 'display:none;' : ''; ?>">
                <?php foreach ($visitors as $visitor): ?>
                    <?php 
                    // Skip visitors with invalid IDs
                    if ($visitor['visitor_id'] <= 0) continue;
                    $member = getMemberInfo($visitor['visitor_id']); 
                    ?>
                    <div class="VisitorCard">
                        <div class="MainCardContent">
                            <div class="AvatarWrap">
                                <a href="/members/<?php echo $visitor['visitor_id']; ?>">
                                    <img src="<?php echo $member['avatar']; ?>" alt="<?php echo $member['name']; ?>" class="Avatar">
                                </a>
                                <?php if ($member['is_online']): ?>
                                    <span class="OnlineIndicator" title="<?php echo !empty($member['location']) ? 'Online at ' . $member['location'] : 'Online'; ?>"></span>
                                <?php endif; ?>
                            </div>
                            <div class="MemberInfo">
                                <div class="MemberName">
                                    <a href="/members/<?php echo $visitor['visitor_id']; ?>" class="Iconic">
                                        <?php if (!empty($member['upic'])): ?>
                                        <img src="<?php echo $member['upic']; ?>" alt="<?php echo $member['name']; ?>" class="Upic">
                                        <?php else: ?>
                                        <?php echo $member['name']; ?>
                                        <?php endif; ?>
                                    </a>
                                    <?php if (!empty($member['title'])): ?>
                                    <span class="MemberTitle"><?php echo $member['title']; ?></span>
                                    <?php endif; ?>
                                </div>
                                <div class="VisitTime">
                                    <time class="<?php echo getTimeClass($visitor['visit_time']); ?>" datetime="<?php echo date('c', strtotime($visitor['visit_time'])); ?>">
                                        <?php echo formatTime($visitor['visit_time']); ?>
                                    </time>
                                </div>
                            </div>
                        </div>
                        <div class="HistoryColumn" style="display: none;">
                            <div class="HistoryHeader">
                                <h4>Visit History</h4>
                            </div>
                            <div class="HistoryLoading">
                                <img src="https://files.gamebanana.com/bitpit/load.gif" alt="Loading..."> Loading...
                            </div>
                            <div class="HistoryContent">
                                <!-- History will be loaded here -->
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</module>

<script>
// Visitors App Namespace to prevent conflicts with other apps
window.VisitorsApp = window.VisitorsApp || {};

(function(VA) {
    'use strict';

    // App-specific constants
    VA.baseUrl = '<?php echo $baseUrl; ?>';
    VA.currentStatsDays = 0; // 0=lifetime; 1=daily; 7=weekly; 30=monthly
    VA.lbDays = 7; // 7=7d default
    VA.lbOffset = 0;
    VA.lbLimit = 10;

    // Tab navigation
    document.addEventListener('click', function(e) {
        const tab = e.target.closest('.Tab');
        if (!tab) return;
        const tabName = tab.getAttribute('data-tab');
        document.querySelectorAll('.Tab').forEach(t => t.classList.remove('TabActive'));
        tab.classList.add('TabActive');
        const visitorsContent = document.getElementById('VisitorsTabContent');
        const statsPanel = document.getElementById('visitorStatsPanel');
        const leaderboardPanel = document.getElementById('visitorLeaderboardPanel');
        const settingsToggle = document.getElementById('SettingsToggle');
        const settingsPanel = document.getElementById('visitorSettingsPanel');
        if (tabName === 'visitors') {
            VA.animateHide(statsPanel, () => { if (settingsToggle) settingsToggle.style.display = ''; });
            VA.animateHide(leaderboardPanel);
            VA.animateShow(visitorsContent);
        } else if (tabName === 'stats') {
            VA.animateHide(visitorsContent, () => { if (settingsToggle) settingsToggle.style.display = 'none'; });
            // Close settings pane when entering statistics tab
            if (settingsPanel && settingsPanel.style.display !== 'none') {
                VA.animateHide(settingsPanel);
            }
            VA.animateHide(leaderboardPanel);
            VA.animateShow(statsPanel);
            VA.loadVisitorStats();
        } else if (tabName === 'leaderboard') {
            VA.animateHide(visitorsContent, () => { if (settingsToggle) settingsToggle.style.display = 'none'; });
            if (settingsPanel && settingsPanel.style.display !== 'none') {
                VA.animateHide(settingsPanel);
            }
            VA.animateHide(statsPanel);
            VA.animateShow(leaderboardPanel);
            VA.loadLeaderboard();
        }
    });

    // Ensure correct initial visibility of settings icon (visitors tab default)
    document.addEventListener('DOMContentLoaded', function() {
        const settingsToggle = document.getElementById('SettingsToggle');
        if (settingsToggle) settingsToggle.style.display = '';
    });
    // Toggle settings panel with subtle animation
    VA.toggleSettingsPanel = function(event) {
        if (event) event.preventDefault();
        const panel = document.getElementById('visitorSettingsPanel');
        const isHidden = panel.style.display === 'none' || panel.style.display === '';
        if (isHidden) {
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(6px)';
            panel.style.display = 'block';
            requestAnimationFrame(() => {
                panel.style.transition = 'opacity 180ms ease, transform 200ms ease';
                panel.style.opacity = '1';
                panel.style.transform = 'translateY(0)';
            });
        } else {
            panel.style.transition = 'opacity 150ms ease, transform 180ms ease';
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(6px)';
            setTimeout(() => { panel.style.display = 'none'; }, 180);
        }
    };

    // Cross-browser panel animations (avoid relying on display for transitions)
    VA.animateShow = function(el) {
        if (!el) return;
        el.style.display = 'block';
        el.style.willChange = 'opacity, transform';
        el.style.opacity = '0';
        el.style.transform = 'translateY(6px)';
        requestAnimationFrame(() => {
            el.style.transition = 'opacity 180ms ease, transform 200ms ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        });
    };

    VA.animateHide = function(el, after) {
        if (!el) { if (after) after(); return; }
        el.style.transition = 'opacity 150ms ease, transform 180ms ease';
        el.style.opacity = '0';
        el.style.transform = 'translateY(6px)';
        setTimeout(() => {
            el.style.display = 'none';
            if (after) after();
        }, 180);
    };

    // Deprecated: stats panel toggling handled by tabs
    VA.toggleStatsPanel = function(event) {
        if (event) event.preventDefault();
        // No-op
    };

    // Load visitor statistics from backend and render
    VA.loadVisitorStats = function() {
        const statsUrl = `${VA.baseUrl}get_visitor_stats.php?profile_id=<?php echo $profileId; ?>`;
        const streakEl = document.getElementById('KpiStreak');
        const streakUserEl = document.getElementById('KpiStreakUser');
        const spark = document.getElementById('ProfileSparkline');
        streakEl.textContent = '…';
        streakUserEl.textContent = '…';

        const xhr = new XMLHttpRequest();
        xhr.open('GET', statsUrl, true);
        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const s = JSON.parse(xhr.responseText);
                    streakEl.textContent = (s.longest_streak ?? 0).toString();
                    streakUserEl.textContent = s.longest_streak_user && s.longest_streak_user.name ? s.longest_streak_user.name : '—';
                } catch (e) {
                    streakEl.textContent = '0';
                    streakUserEl.textContent = '—';
                }
            } else {
                streakEl.textContent = '0';
                streakUserEl.textContent = '—';
            }
        };
        xhr.onerror = function() {
            streakEl.textContent = '0';
            streakUserEl.textContent = '—';
        };
        xhr.send();

        // Load activity sparkline based on selected period (map to sensible window)
        const ax = new XMLHttpRequest();
        const sparkDays = (function(d){ if (d===0) return 90; if (d<=1) return 7; if (d<=7) return 28; return 90; })(VA.currentStatsDays);
        ax.open('GET', `${VA.baseUrl}get_activity.php?profile_id=<?php echo $profileId; ?>&days=${sparkDays}` , true);
        ax.onload = function() {
            if (ax.status === 200) {
                try {
                    const a = JSON.parse(ax.responseText);
                    VA.renderSparkline(spark, a.counts, a.dates);
                } catch (e) {
                    // ignore rendering error
                }
            }
        };
        ax.send();

        // KPIs for selected period (days=0 means lifetime)
        const px = new XMLHttpRequest();
        px.open('GET', `${VA.baseUrl}get_stats_period.php?profile_id=<?php echo $profileId; ?>&days=${VA.currentStatsDays}`, true);
        px.onload = function() {
            if (px.status === 200) {
                try {
                    const p = JSON.parse(px.responseText);
                    document.getElementById('KpiUnique').textContent = p.unique_visitors ?? 0;
                    document.getElementById('KpiVisits').textContent = p.visits ?? 0;
                    document.getElementById('KpiDays').textContent = p.active_days ?? 0;
                } catch (e) {}
            }
        };
        px.send();

        // Top visitors for selected period (page 1)
        const tx = new XMLHttpRequest();
        tx.open('GET', `${VA.baseUrl}get_top_visitors.php?profile_id=<?php echo $profileId; ?>&days=${VA.currentStatsDays}&limit=10`, true);
        tx.onload = function() {
            if (tx.status === 200) {
                try { VA.renderTopFans(JSON.parse(tx.responseText)); } catch (e) {}
            }
        };
        tx.send();
    };

    // Render a compact sparkline with endpoint labels and hover tooltip
    VA.renderSparkline = function(svgEl, values, dates) {
        if (!svgEl || !values || values.length === 0) { if (svgEl) svgEl.innerHTML=''; return; }
        const w = 100, h = 24, pad = 2;
        const max = Math.max(1, Math.max(...values));
        const avg = values.reduce((a,b)=>a+b,0)/values.length;
        const stepX = (w - pad * 2) / Math.max(1, values.length - 1);
        const yFor = (v) => h - pad - (v / max) * (h - pad * 2);
        const pts = values.map((v, i) => `${(pad + i * stepX).toFixed(2)},${yFor(v).toFixed(2)}`).join(' ');
        const avgY = yFor(avg).toFixed(2);
        const startLabel = dates && dates.length ? VA.formatShortDate(dates[0]) : '';
        const endLabel = dates && dates.length ? VA.formatShortDate(dates[dates.length-1]) : '';

        svgEl.setAttribute('viewBox', `0 0 ${w} ${h}`);
        svgEl.innerHTML = `
            <line x1="0" y1="${h-0.5}" x2="${w}" y2="${h-0.5}" stroke="rgba(255,255,255,0.15)" stroke-width="0.5" />
            <line x1="0" y1="${avgY}" x2="${w}" y2="${avgY}" stroke="rgba(255,255,255,0.25)" stroke-dasharray="2,2" stroke-width="0.5" />
            <polyline id="SparkLine" points="${pts}" fill="none" stroke="rgba(255,255,255,0.9)" stroke-width="1.25" />
            <text class="AxisLabel" x="1" y="${h-1}" fill="rgba(255,255,255,0.6)" font-size="2">${startLabel}</text>
            <text class="AxisLabel" x="${w-1}" y="${h-1}" text-anchor="end" fill="rgba(255,255,255,0.6)" font-size="2">${endLabel}</text>
            <text class="AxisLabel" x="${w-1}" y="${pad+3}" text-anchor="end" fill="rgba(255,255,255,0.6)" font-size="2">max ${max}</text>
            <line id="SparkGuide" x1="0" y1="${pad}" x2="0" y2="${h-pad}" stroke="rgba(255,255,255,0.3)" stroke-width="0.5" style="display:none" />
            <circle id="SparkDot" cx="0" cy="0" r="1.2" fill="#fff" style="display:none" />
        `;

        VA.setupSparklineInteractions(svgEl, values, dates || []);
    };

    VA.setupSparklineInteractions = function(svgEl, values, dates) {
        const tooltip = document.getElementById('SparkTooltip');
        const guide = svgEl.querySelector('#SparkGuide');
        const dot = svgEl.querySelector('#SparkDot');
        const w = 100, h = 24, pad = 2;
        const max = Math.max(1, Math.max(...values));
        const yFor = (v) => h - pad - (v / max) * (h - pad * 2);
        const stepX = (w - pad * 2) / Math.max(1, values.length - 1);
        const getIndexAtX = (x) => Math.max(0, Math.min(values.length - 1, Math.round((x - pad) / stepX)));

        function showAt(evt) {
            const rect = svgEl.getBoundingClientRect();
            const relX = ((evt.clientX - rect.left) / rect.width) * w;
            const idx = getIndexAtX(relX);
            const cx = pad + idx * stepX;
            const cy = yFor(values[idx]);
            guide.setAttribute('x1', cx); guide.setAttribute('x2', cx);
            guide.style.display = 'block';
            dot.setAttribute('cx', cx); dot.setAttribute('cy', cy);
            dot.style.display = 'block';
            if (tooltip) {
                tooltip.style.display = 'block';
                tooltip.textContent = `${values[idx]} on ${VA.formatLongDate(dates[idx])}`;
                const wrap = svgEl.parentElement.getBoundingClientRect();
                const tx = rect.left + (cx / w) * rect.width;
                const ty = rect.top + (cy / h) * rect.height;
                tooltip.style.left = `${tx - wrap.left}px`;
                tooltip.style.top = `${ty - wrap.top - 8}px`;
            }
        }
        function hide() {
            guide.style.display = 'none';
            dot.style.display = 'none';
            if (tooltip) tooltip.style.display = 'none';
        }
        svgEl.onmousemove = showAt;
        svgEl.onmouseleave = hide;
    };

    VA.formatShortDate = function(iso) {
        if (!iso) return '';
        const d = new Date(iso);
        const m = (d.getUTCMonth()+1).toString().padStart(2,'0');
        const day = d.getUTCDate().toString().padStart(2,'0');
        return `${m}/${day}`;
    };

    VA.formatLongDate = function(iso) {
        if (!iso) return '';
        const d = new Date(iso);
        return d.toUTCString().slice(5,16);
    };

    // Stats period controls
    document.addEventListener('click', function(e) {
        const chip = e.target.closest('.RangeChip');
        if (!chip || !chip.closest('#StatsFilters')) return;
        document.querySelectorAll('#StatsFilters .RangeChip').forEach(c => c.classList.remove('RangeActive'));
        chip.classList.add('RangeActive');
        VA.currentStatsDays = parseInt(chip.getAttribute('data-days') || '0', 10);
        VA.loadVisitorStats();
    });

    VA.renderTopFans = function(items) {
        const el = document.getElementById('TopFansList');
        if (!el) return;
        if (!items || items.length === 0) { el.innerHTML = '<div class="Empty">No data</div>'; return; }
        el.innerHTML = items.map((it) => `
            <div class="TopItem">
                <img class="Avatar" src="${(it.visitor && it.visitor.avatar) ? it.visitor.avatar : 'https://gamebanana.com/img/default_avatar.png'}" alt="" />
                <div>
                    <div><a href="/members/${it.visitor.id}">${it.visitor.name}</a></div>
                    <div class="TopMeta">${it.visits} total visits • ${it.unique_days} unique days</div>
                </div>
            </div>
        `).join('');
    };

    // Leaderboards
    VA.loadLeaderboard = function() {
        const list = document.getElementById('LbList');
        const pageInfo = document.getElementById('LbPageInfo');
        list.innerHTML = '<div class="HistoryLoading"><img src="https://files.gamebanana.com/bitpit/load.gif" alt=""> Loading...</div>';
        const req = new XMLHttpRequest();
        const url = `${VA.baseUrl}get_global_leaderboard.php?days=${VA.lbDays}&limit=${VA.lbLimit}&offset=${VA.lbOffset}`;
        req.open('GET', url, true);
        req.onload = function(){
            if (req.status === 200) {
                try {
                    const items = JSON.parse(req.responseText);
                    list.innerHTML = items.map(it => `
                        <div class="TopItem">
                            <img class="Avatar" src="${it.visitor.avatar}" alt="" />
                            <div>
                                <div><a href="/members/${it.visitor.id}">${it.visitor.name}</a></div>
                                <div class="TopMeta">${it.visits} total • ${it.unique_days} unique • ${it.current_streak || 0} streak</div>
                            </div>
                        </div>
                    `).join('');
                    const page = Math.floor(VA.lbOffset / VA.lbLimit) + 1;
                    pageInfo.textContent = `Page ${page}`;
                    document.getElementById('LbPrev').disabled = VA.lbOffset <= 0;
                    document.getElementById('LbNext').disabled = items.length < VA.lbLimit;
                } catch (e) { list.innerHTML = '<div class="Empty">Error</div>'; }
            } else { list.innerHTML = '<div class="Empty">Error</div>'; }
        };
        req.onerror = function(){ list.innerHTML = '<div class="Empty">Network error</div>'; };
        req.send();
    };

    document.addEventListener('click', function(e){
        const chip = e.target.closest('#LbFilters .RangeChip');
        if (chip) {
            document.querySelectorAll('#LbFilters .RangeChip[data-days]').forEach(c => c.classList.remove('RangeActive'));
            chip.classList.add('RangeActive');
            VA.lbDays = parseInt(chip.getAttribute('data-days') || '0', 10);
            VA.lbOffset = 0;
            VA.loadLeaderboard();
            return;
        }
        if (e.target && e.target.id === 'LbPrev') {
            if (VA.lbOffset >= VA.lbLimit) { VA.lbOffset -= VA.lbLimit; VA.loadLeaderboard(); }
            return;
        }
        if (e.target && e.target.id === 'LbNext') {
            VA.lbOffset += VA.lbLimit; VA.loadLeaderboard();
            return;
        }
    });

    // Heatmap removed

    // Save settings and apply them immediately
    VA.saveSettings = function() {
        const displayMode = document.getElementById('displayMode').value;
        const visitorCount = document.getElementById('visitorCount').value;
        const notifyOnVisit = document.getElementById('notifyOnVisit').checked ? 'true' : 'false';

        // Show save feedback
        const saveFeedback = document.getElementById('saveFeedback');
        saveFeedback.style.display = 'flex';
        saveFeedback.querySelector('.feedbackText').textContent = 'Saving settings...';

        // Disable buttons during save
        const buttons = document.querySelectorAll('.SettingActions button');
        buttons.forEach(button => {
            button.disabled = true;
        });

        // Save settings via AJAX
        const xhr = new XMLHttpRequest();
        // Save to current host visitors app
        xhr.open('POST', VA.baseUrl + 'save_settings.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onload = function() {
            if (xhr.status === 200) {
                // Update feedback message
                saveFeedback.querySelector('.feedbackText').innerHTML = 'Settings saved successfully <img src="https://files.gamebanana.com/bitpit/ok.gif">';

                // Apply display mode immediately
                VA.applyDisplayMode(displayMode);

                // Update notification settings
                VA.updateNotificationSettings(notifyOnVisit === 'true');

                // If visitor count changed, fetch new visitor data
                if (parseInt(visitorCount) !== <?php echo (int)$visitorCount; ?>) {
                    // Update feedback message
                    saveFeedback.querySelector('.feedbackText').innerHTML = 'Loading <img src="https://files.gamebanana.com/bitpit/load.gif">';

                    // Fetch new visitor data via AJAX
                    VA.fetchVisitorData(visitorCount);
                } else {
                    // Hide settings panel and feedback after a short delay
                    setTimeout(() => {
                        saveFeedback.style.display = 'none';
                        document.getElementById('visitorSettingsPanel').style.display = 'none';
                        // Re-enable buttons
                        buttons.forEach(button => {
                            button.disabled = false;
                        });
                    }, 1200);
                }
            } else {
                // Show error
                saveFeedback.querySelector('.feedbackText').innerHTML = 'Error saving settings <img src="https://files.gamebanana.com/bitpit/error.gif">';

                // Re-enable buttons
                buttons.forEach(button => {
                    button.disabled = false;
                });

                // Hide error after a delay
                setTimeout(() => {
                    saveFeedback.style.display = 'none';
                }, 3000);
            }
        };

        xhr.onerror = function() {
            // Show error
            saveFeedback.querySelector('.feedbackText').innerHTML = 'Network error. Please try again <img src="https://files.gamebanana.com/bitpit/error.gif">';

            // Re-enable buttons
            buttons.forEach(button => {
                button.disabled = false;
            });

            // Hide error after a delay
            setTimeout(() => {
                saveFeedback.style.display = 'none';
            }, 3000);
        };

        // Send the request with profile ID and settings
        xhr.send('profile_id=<?php echo $profileId; ?>&display_mode=' + displayMode + '&visitor_count=' + visitorCount + '&notify_on_visit=' + notifyOnVisit + '&_idMember=<?php echo $visitorId; ?>');
    };

    // Fetch visitor data via AJAX
    VA.fetchVisitorData = function(count) {
        const xhr = new XMLHttpRequest();
        // Fetch from current host visitors app
        xhr.open('GET', `${VA.baseUrl}get_visitors.php?profile_id=<?php echo $profileId; ?>&count=${count}`, true);

        xhr.onload = function() {
            const saveFeedback = document.getElementById('saveFeedback');
            const buttons = document.querySelectorAll('.SettingActions button');

            if (xhr.status === 200) {
                try {
                    // Parse visitor data
                    const visitors = JSON.parse(xhr.responseText);

                    // Update feedback message
                    saveFeedback.querySelector('.feedbackText').innerHTML = 'Updating visitor list <img src="https://files.gamebanana.com/bitpit/load.gif">';

                    // Update visitor lists
                    VA.updateVisitorLists(visitors);

                    // Hide settings panel and feedback after a short delay
                    setTimeout(() => {
                        saveFeedback.style.display = 'none';
                        document.getElementById('visitorSettingsPanel').style.display = 'none';
                        // Re-enable buttons
                        buttons.forEach(button => {
                            button.disabled = false;
                        });
                    }, 1200);

                } catch (e) {
                    console.error('Error parsing visitor data:', e);
                    saveFeedback.querySelector('.feedbackText').innerHTML = 'Error updating visitors <img src="https://files.gamebanana.com/bitpit/error.gif">';

                    // Re-enable buttons
                    buttons.forEach(button => {
                        button.disabled = false;
                    });

                    // Hide error after a delay
                    setTimeout(() => {
                        saveFeedback.style.display = 'none';
                    }, 3000);
                }
            } else {
                saveFeedback.querySelector('.feedbackText').innerHTML = 'Error fetching visitor data <img src="https://files.gamebanana.com/bitpit/error.gif">';

                // Re-enable buttons
                buttons.forEach(button => {
                    button.disabled = false;
                });

                // Hide error after a delay
                setTimeout(() => {
                    saveFeedback.style.display = 'none';
                }, 3000);
            }
        };

        xhr.onerror = function() {
            const saveFeedback = document.getElementById('saveFeedback');
            saveFeedback.querySelector('.feedbackText').innerHTML = 'Network error fetching visitors <img src="https://files.gamebanana.com/bitpit/error.gif">';

            // Re-enable buttons
            const buttons = document.querySelectorAll('.SettingActions button');
            buttons.forEach(button => {
                button.disabled = false;
            });

            // Hide error after a delay
            setTimeout(() => {
                saveFeedback.style.display = 'none';
            }, 3000);
        };

        xhr.send();
    };

    // Update visitor lists in both display modes
    VA.updateVisitorLists = function(visitors) {
        // Update lite view (table)
        const liteContainer = document.querySelector('#RecentVisitors .Classic');
        let liteHTML = `
            <columnheadings>
                <columnheading>Visitor</columnheading>
                <columnheading>Time</columnheading>
            </columnheadings>
        `;

        // Update full view (grid)
        const fullContainer = document.querySelector('#RecentVisitors .VisitorsGrid');
        let fullHTML = '';

        // Generate HTML for both views
        visitors.forEach((visitor, index) => {
            const member = visitor.visitor;
            const time = visitor.visit_time;
            const streak = visitor.streak || 0;

            // Lite view HTML
            liteHTML += `
                <record>
                    <recordcell>
                        <a href="/members/${member.id}" class="Iconic">
                            ${member.is_online ? `<span class="OnlineDot" title="${member.location ? 'Online at ' + member.location : 'Online'}"></span>` : ''}
                            ${member.name}
                        </a>
                    </recordcell>
                    <recordcell>
                        <time class="${time.class}" datetime="${time.datetime}">
                            ${time.formatted}
                        </time>
                    </recordcell>
                </record>
            `;

            // Full view HTML - Updated with new design, history column, and class for alternating backgrounds
            fullHTML += `
                <div class="VisitorCard">
                    <div class="MainCardContent">
                        <div class="AvatarWrap">
                            <a href="/members/${member.id}">
                                <img src="${member.avatar}" alt="${member.name}" class="Avatar">
                            </a>
                            ${member.is_online ?
                              `<span class="OnlineIndicator" title="${member.location ? 'Online at ' + member.location : 'Online'}"></span>` : ''}
                        </div>
                        <div class="MemberInfo">
                            <div class="MemberName">
                                <a href="/members/${member.id}" class="Iconic">
                                   ${member.upic ?
                                    `<img src="${member.upic}" alt="${member.name}" class="Upic">` :
                                    `${member.name}`}
                                </a>
                                ${member.title ? `<span class="MemberTitle">${member.title}</span>` : ''}
                            </div>
                            <div class="VisitTime">
                                <time class="${time.class}" datetime="${time.datetime}">
                                    ${time.formatted}
                                </time>
                            </div>
                        </div>
                    </div>
                    <div class="HistoryColumn" style="display: none;">
                        <div class="HistoryHeader">
                            <h4>Visit History</h4>
                        </div>
                        <div class="HistoryLoading">
                            <img src="https://files.gamebanana.com/bitpit/load.gif" alt="Loading..."> Loading...
                        </div>
                        <div class="HistoryContent">
                            <!-- History will be loaded here -->
                        </div>
                    </div>
                </div>
            `;
        });

        // Update the DOM
        liteContainer.innerHTML = liteHTML;
        fullContainer.innerHTML = fullHTML;
    };

    // Apply display mode without page reload
    VA.applyDisplayMode = function(mode) {
        const liteDisplayEl = document.querySelector('#RecentVisitors .Classic');
        const fullDisplayEl = document.querySelector('#RecentVisitors .VisitorsGrid');

        // Apply the mode
        if (mode === 'full') {
            liteDisplayEl.style.display = 'none';
            fullDisplayEl.style.display = 'grid';
        } else {
            liteDisplayEl.style.display = '';
            fullDisplayEl.style.display = 'none';
        }
    };

    // Initialize display on page load
    document.addEventListener('DOMContentLoaded', function() {
        const currentMode = '<?php echo $displayMode; ?>';
        const liteDisplayEl = document.querySelector('#RecentVisitors .Classic');
        const fullDisplayEl = document.querySelector('#RecentVisitors .VisitorsGrid');

        // Ensure both display modes have opacity 1 at the start
        liteDisplayEl.style.opacity = '1';
        fullDisplayEl.style.opacity = '1';

        // Set initial display based on server-side mode
        if (currentMode === 'full') {
            liteDisplayEl.style.display = 'none';
            fullDisplayEl.style.display = 'grid';
        } else {
            liteDisplayEl.style.display = '';
            fullDisplayEl.style.display = 'none';
        }

        // Ensure stats panel is hidden initially
        document.getElementById('visitorStatsPanel').style.display = 'none';
    });

    // Function to toggle visit history
    VA.toggleVisitHistory = function(buttonElement, visitorId, visitorName) {
        // Find the parent visitor card
        const visitorCard = buttonElement.closest('.VisitorCard');
        const historyColumn = visitorCard.querySelector('.HistoryColumn');
        const loadingEl = historyColumn.querySelector('.HistoryLoading');
        const contentEl = historyColumn.querySelector('.HistoryContent');

        // If history column is already visible, hide it
        if (historyColumn.style.display === 'block') {
            historyColumn.style.display = 'none';
            return;
        }

        // Show history column and loading indicator
        historyColumn.style.display = 'block';
        loadingEl.style.display = 'flex';
        contentEl.style.display = 'none';
        contentEl.innerHTML = '';

        // Fetch visit history via AJAX
        const xhr = new XMLHttpRequest();
        xhr.open('GET', `${VA.baseUrl}get_visit_history.php?profile_id=<?php echo $profileId; ?>&visitor_id=${visitorId}&limit=5`, true);

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    // Parse visitor history data
                    const historyData = JSON.parse(xhr.responseText);

                    // Hide loading indicator
                    loadingEl.style.display = 'none';
                    contentEl.style.display = 'block';

                    // Build HTML for visit history
                    let historyHTML = '';

                    if (historyData.length === 0) {
                        historyHTML = '<div class="EmptyHistory">No visit history found</div>';
                    } else {
                        historyData.forEach((visit) => {
                            historyHTML += `
                                <div class="HistoryVisitItem">
                                    <time class="${visit.time_class}" datetime="${visit.datetime}" style="flex: 1;">
                                        ${visit.formatted_time}
                                    </time>
                                    ${visit.streak > 2 ?
                                    `<span class="StreakIndicator" title="${visit.streak}-day visit streak" style="margin-left: 10px;">
                                        🔥 ${visit.streak}
                                    </span>` : ''}
                                </div>
                            `;
                        });
                    }

                    // Update the content
                    contentEl.innerHTML = historyHTML;

                } catch (e) {
                    console.error('Error parsing visit history:', e);
                    contentEl.innerHTML = '<div class="ErrorMessage">Error loading visit history</div>';
                    loadingEl.style.display = 'none';
                    contentEl.style.display = 'block';
                }
            } else {
                contentEl.innerHTML = '<div class="ErrorMessage">Error loading visit history</div>';
                loadingEl.style.display = 'none';
                contentEl.style.display = 'block';
            }
        };

        xhr.onerror = function() {
            contentEl.innerHTML = '<div class="ErrorMessage">Network error. Please try again.</div>';
            loadingEl.style.display = 'none';
            contentEl.style.display = 'block';
        };

        xhr.send();
    };

    // Remove function to close visit history modal since we're using inline history
    VA.closeVisitHistory = function() {
        // Function kept for compatibility but doesn't do anything now
        return;
    };

    // Add notification system for visitor notifications
    VA.showVisitorNotification = function(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'visitor-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transition: all 0.3s ease;
            transform: translateX(100%);
            ${type === 'success' ? 'background: linear-gradient(135deg, #4caf50, #45a049);' :
              type === 'error' ? 'background: linear-gradient(135deg, #f44336, #d32f2f);' :
              'background: linear-gradient(135deg, #2196f3, #1976d2);'}
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    };

    // Visitor notification checking system
    VA.lastVisitorCheck = new Date().toISOString().slice(0, 19).replace('T', ' ');
    VA.visitorCheckInterval = null;
    VA.notificationsEnabled = <?php echo ($settings['notify_on_visit'] ?? 'true') === 'true' ? 'true' : 'false'; ?>;

    // Check for new visitors
    VA.checkForNewVisitors = function() {
        if (!VA.notificationsEnabled) {
            return;
        }

        const xhr = new XMLHttpRequest();
        xhr.open('GET', `${VA.baseUrl}check_new_visitors.php?profile_id=<?php echo $profileId; ?>&last_check=${encodeURIComponent(VA.lastVisitorCheck)}`, true);

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.new_visitors && response.new_visitors.length > 0) {
                        // Show notification for new visitors
                        const count = response.count;
                        const message = count === 1 ?
                            `${response.new_visitors[0].visitor.name} visited your profile!` :
                            `${count} new visitors to your profile!`;

                        VA.showVisitorNotification(message, 'info');

                        // Reload visitor data to show new visitors
                        const currentCount = document.getElementById('visitorCount').value || 10;
                        VA.fetchVisitorData(currentCount);
                    }

                    // Update last check time
                    if (response.current_time) {
                        VA.lastVisitorCheck = response.current_time;
                    }
                } catch (e) {
                    console.error('Error parsing visitor check response:', e);
                }
            }
        };

        xhr.onerror = function() {
            console.error('Error checking for new visitors');
        };

        xhr.send();
    };

    // Start visitor checking if notifications are enabled and user is profile owner
    VA.startVisitorChecking = function() {
        if (VA.notificationsEnabled && <?php echo $isOwner ? 'true' : 'false'; ?>) {
            // Check every 30 seconds
            VA.visitorCheckInterval = setInterval(VA.checkForNewVisitors, 30000);
        }
    };

    // Stop visitor checking
    VA.stopVisitorChecking = function() {
        if (VA.visitorCheckInterval) {
            clearInterval(VA.visitorCheckInterval);
            VA.visitorCheckInterval = null;
        }
    };

    // Update notification settings
    VA.updateNotificationSettings = function(enabled) {
        VA.notificationsEnabled = enabled;
        if (enabled && <?php echo $isOwner ? 'true' : 'false'; ?>) {
            VA.startVisitorChecking();
        } else {
            VA.stopVisitorChecking();
        }
    };

    // Initialize visitor checking on page load
    document.addEventListener('DOMContentLoaded', function() {
        VA.startVisitorChecking();
    });

    // Cleanup intervals when page unloads
    window.addEventListener('beforeunload', function() {
        VA.stopVisitorChecking();
    });

    // Expose global functions for HTML onclick handlers
    window.toggleSettingsPanel = VA.toggleSettingsPanel;
    window.saveSettings = VA.saveSettings;
    window.toggleVisitHistory = VA.toggleVisitHistory;
    window.closeVisitHistory = VA.closeVisitHistory;

})(window.VisitorsApp);
</script>
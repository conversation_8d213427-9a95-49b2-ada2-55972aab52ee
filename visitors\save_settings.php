<?php
require_once __DIR__ . '/common.php';
// Allow cross-origin requests from GameBanana
vb_enable_cors_json(['https://gamebanana.com', 'https://kurisumaki.se/']);

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Just exit with 200 OK status
    http_response_code(200);
    exit;
}

// Enforce rate limiting (5 requests per minute)
vb_enforce_rate_limit(10, 60);

require_once 'db_config.php';

// Enforce allowed Origin/Referer
vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$profileId = isset($_POST['profile_id']) ? (int)$_POST['profile_id'] : 0;
if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

// Check for required parameters
if (!isset($_POST['profile_id']) || !isset($_POST['display_mode']) || !isset($_POST['visitor_count'])) {
    http_response_code(400);
    echo "Missing required parameters";
    exit;
}

// Get the parameters
$profileId = (int)$_POST['profile_id'];
$actorId = isset($_POST['_idMember']) ? (int)$_POST['_idMember'] : 0;
$displayMode = $_POST['display_mode'] === 'full' ? 'full' : 'lite';
$rawVisitorCount = $_POST['visitor_count'];
$visitorCount = (int)$rawVisitorCount;

// Debug logging
error_log("Visitor count debug - Raw: '{$rawVisitorCount}', Parsed: {$visitorCount}");

// Validate visitor count
if ($visitorCount < 1 || $visitorCount > 50) {
    error_log("Visitor count validation failed: {$visitorCount}, defaulting to 10");
    $visitorCount = 10; // Default to 10 if invalid
}

// Create settings array
$settings = [
    'display_mode' => $displayMode,
    'visitor_count' => $visitorCount
];

// Debug logging
error_log("Settings to save: " . json_encode($settings));

// Authorization: only the owner (profileId) can save their settings
if ($actorId !== $profileId || $profileId <= 0) {
    http_response_code(403);
    echo "Forbidden";
    exit;
}

// Save settings
try {
    $settingsJson = json_encode($settings);
    $query = "INSERT INTO profile_visitor_settings (profile_id, settings) 
             VALUES (?, ?)
             ON DUPLICATE KEY UPDATE settings = ?";
    
    $stmt = $db->prepare($query);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $db->error);
    }
    
    $stmt->bind_param('iss', $profileId, $settingsJson, $settingsJson);
    $result = $stmt->execute();
    $stmt->close();
    
    if ($result) {
        echo "Settings saved successfully";
    } else {
        throw new Exception("Failed to save settings");
    }
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error saving settings: " . $e->getMessage());
    echo "Error saving settings";
}
?> 
<?php
// Set the default timezone
date_default_timezone_set('UTC');

// Allow cross-origin requests from GameBanana
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header("Content-Type: application/json");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Just exit with 200 OK status
    http_response_code(200);
    exit;
}

// Enforce rate limiting (60 requests per 5 minutes)
vb_enforce_rate_limit(60, 300);

require_once 'db_config.php';
require_once 'track_visitor.php';
require_once 'gamebanana_api.php';

// Enforce allowed Origin/Referer
vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$profileId = isset($_GET['_idProfile']) ? (int)$_GET['_idProfile'] : 0;
if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

// Check for required parameters
if (!isset($_GET['profile_id']) || !isset($_GET['last_check'])) {
    vb_json_response(['error' => 'Missing required parameters'], 400);
}

// Get parameters
$profileId = (int)$_GET['profile_id'];
$lastCheck = $_GET['last_check'];

// Validate profile ID
if ($profileId <= 0) {
    vb_json_response(['error' => 'Invalid profile ID'], 400);
}

// Validate last_check timestamp
if (!$lastCheck || !strtotime($lastCheck)) {
    vb_json_response(['error' => 'Invalid last_check timestamp'], 400);
}

try {
    // Get new visitors since last check
    $query = "SELECT visitor_id, visit_time
             FROM profile_visitors
             WHERE profile_id = ? 
             AND visit_time > ?
             ORDER BY visit_time DESC";
    
    $stmt = $db->prepare($query);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $db->error);
    }
    
    $stmt->bind_param('is', $profileId, $lastCheck);
    $stmt->execute();
    
    $result = $stmt->get_result();
    $newVisitors = [];
    
    while ($row = $result->fetch_assoc()) {
        // Skip invalid member IDs
        if ($row['visitor_id'] <= 0) {
            continue;
        }
        
        $member = getMemberDetails($row['visitor_id']);
        
        // Use fallback data if API call fails
        if (!$member) {
            $member = [
                'id' => $row['visitor_id'],
                'name' => 'Member_' . $row['visitor_id'],
                'avatar' => 'https://gamebanana.com/img/default_avatar.png',
                'title' => '',
                'medal_count' => 0,
                'is_founder' => false,
                'is_uploader' => false,
                'is_online' => false,
                'location' => ''
            ];
        }
        
        // Format timestamp
        $timestamp = strtotime($row['visit_time']);
        $formattedTime = formatTime($row['visit_time']);
        $timeClass = getTimeClass($row['visit_time']);
        $datetime = date('c', $timestamp);
        
        // Add visitor to array
        $newVisitors[] = [
            'visitor' => $member,
            'visit_time' => [
                'raw' => $row['visit_time'],
                'formatted' => $formattedTime,
                'class' => $timeClass,
                'datetime' => $datetime
            ]
        ];
    }
    
    $stmt->close();
    
    // Return response with new visitors and current timestamp
    vb_json_response([
        'new_visitors' => $newVisitors,
        'current_time' => date('Y-m-d H:i:s'),
        'count' => count($newVisitors)
    ]);
    
} catch (Exception $e) {
    error_log("Error checking new visitors: " . $e->getMessage());
    vb_json_response(['error' => 'Database error'], 500);
}

// Helper functions (simplified versions of those in main.php)
function formatTime($timestamp) { return vb_format_time($timestamp); }
function getTimeClass($timestamp) { return vb_time_class($timestamp); }
?>

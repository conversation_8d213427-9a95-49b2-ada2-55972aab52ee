<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visitors App Notification Test</title>
    <link rel="stylesheet" type="text/css" href="visitors.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
        }
        .test-button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.error {
            background: #f44336;
        }
        .test-button.error:hover {
            background: #d32f2f;
        }
        .test-button.info {
            background: #2196f3;
        }
        .test-button.info:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Visitors App Notification System Test</h1>
        <p>This page tests the notification system for the Visitors app.</p>
        
        <h3>Test Notifications:</h3>
        <button class="test-button" onclick="testSuccessNotification()">Test Success Notification</button>
        <button class="test-button error" onclick="testErrorNotification()">Test Error Notification</button>
        <button class="test-button info" onclick="testInfoNotification()">Test Info Notification</button>
        <button class="test-button info" onclick="testVisitorNotification()">Test Visitor Notification</button>
        
        <h3>Test Multiple Notifications:</h3>
        <button class="test-button" onclick="testMultipleNotifications()">Test Multiple Notifications</button>
        
        <h3>Instructions:</h3>
        <ul>
            <li>Click the buttons above to test different notification types</li>
            <li>Notifications should appear in the top-right corner</li>
            <li>They should slide in from the right and auto-dismiss after 3 seconds</li>
            <li>Multiple notifications should stack properly</li>
        </ul>
    </div>

    <script>
        // Copy the notification function from the main app
        function showVisitorNotification(message, type = 'success') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'visitor-notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                transition: all 0.3s ease;
                transform: translateX(100%);
                ${type === 'success' ? 'background: linear-gradient(135deg, #4caf50, #45a049);' : 
                  type === 'error' ? 'background: linear-gradient(135deg, #f44336, #d32f2f);' : 
                  'background: linear-gradient(135deg, #2196f3, #1976d2);'}
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function testSuccessNotification() {
            showVisitorNotification('Settings saved successfully!', 'success');
        }

        function testErrorNotification() {
            showVisitorNotification('Error saving settings', 'error');
        }

        function testInfoNotification() {
            showVisitorNotification('Information message', 'info');
        }

        function testVisitorNotification() {
            showVisitorNotification('TestUser visited your profile!', 'info');
        }

        function testMultipleNotifications() {
            showVisitorNotification('First notification', 'success');
            setTimeout(() => showVisitorNotification('Second notification', 'info'), 500);
            setTimeout(() => showVisitorNotification('Third notification', 'error'), 1000);
        }
    </script>
</body>
</html>

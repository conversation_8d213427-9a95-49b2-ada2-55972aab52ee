<?php
/**
 * AJAX endpoint for collecting trading cards
 */

require_once 'common.php';
require_once 'db_config.php';
require_once 'card_system.php';

// Enable CORS for GameBanana
tc_enable_cors();

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Require allowed origin
tc_require_allowed_origin();

// Enforce rate limiting
vb_enforce_rate_limit(30, 300); // 30 requests per 5 minutes

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    tc_json_response(['error' => 'Method not allowed'], 405);
}

// Get parameters
$collectorId = isset($_POST['collector_id']) ? (int)$_POST['collector_id'] : 0;
$cardOwnerId = isset($_POST['card_owner_id']) ? (int)$_POST['card_owner_id'] : 0;

// Validate parameters
if ($collectorId <= 0 || $cardOwnerId <= 0) {
    tc_json_response(['error' => 'Invalid member IDs'], 400);
}

// Allow self-collection - app owners can collect their own cards

// Authenticate the collector
vb_require_app_authentication($collectorId, 1080);

// Check if collector has an initialized trading card (don't create new one)
$collectorCard = tc_getMemberCard($collectorId, false);
if (!$collectorCard) {
    tc_json_response(['error' => 'You need to initialize your trading card first. Visit https://gamebanana.com/apps/1080'], 403);
}

// Attempt to collect the card
$result = tc_collectCard($collectorId, $cardOwnerId);

// Return the result
tc_json_response($result);
?>
